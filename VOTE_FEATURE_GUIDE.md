# 投票功能使用指南

## 功能概述

在文章编辑器中新增了投票功能，支持单选和多选投票。当用户点击确定时，系统会自动调用后端API创建或更新投票。

## API接口

### 创建投票接口
- **路径**: `/api/news/v3/articlevote/createVote`
- **方法**: POST
- **请求参数**:
```json
{
    "title": "测试多选投票标题",
    "vote_type": 2,
    "max_choices": 1,
    "options": [
        {
            "name": "asdada",
            "value": "asdadag"
        },
        {
            "name": "单选选项2",
            "value": "option2"
        },
        {
            "name": "单选选项3",
            "value": "option3"
        }
    ]
}
```
- **返回示例**:
```json
{
    "error_code": 0,
    "data": {
        "vote_id": "6"
    }
}
```

### 更新投票接口
- **路径**: `/api/news/v3/articlevote/updateVote`
- **方法**: POST
- **请求参数**:
```json
{
    "vote_id": 8,
    "title": "修改后 - 测试多选投票标题",
    "vote_type": 2,
    "max_choices": 1,
    "options": [
        {
            "name": "asdada",
            "value": "asdadag"
        },
        {
            "name": "单选选项2",
            "value": "option2"
        },
        {
            "name": "单选选项3",
            "value": "option3"
        }
    ]
}
```

## 参数说明

- `title`: 投票标题（必填）
- `vote_type`: 投票类型（可选，1=单选，2=多选，默认1）
- `max_choices`: 最大选择数（可选，多选时生效，用户最大选项数量）
- `options`: 投票选项（必填，至少2个）
  - `name`: 选项显示文本
  - `value`: 选项值

## 使用方法

### 在文章编辑器中使用

1. 在文章编辑页面，点击编辑器工具栏中的"投票"按钮
2. 填写投票标题
3. 选择投票类型（单选/多选）
4. 如果是多选，设置最大选择数
5. 添加投票选项（至少2个）
6. 点击确定

### 新增投票流程

1. 用户填写投票信息
2. 点击确定按钮
3. 系统验证投票数据
4. 调用 `createVote` API 创建投票
5. 获取返回的 `vote_id`
6. 将投票插入到编辑器中

### 编辑投票流程

1. 用户修改投票信息
2. 点击确定按钮
3. 系统验证投票数据
4. 调用 `updateVote` API 更新投票
5. 更新编辑器中的投票内容

## 数据验证

系统会自动进行以下验证：

1. 投票标题不能为空
2. 投票选项至少需要2个
3. 多选投票必须设置最大选择数
4. 最大选择数不能超过选项总数
5. 选项的文本和值都不能为空

## 技术实现

### 文件修改

1. **src/services/winesmell/index.js**: 添加了 `createVote` 和 `updateVote` API接口
2. **src/components/WysiwygEditor/index.vue**: 
   - 修改了 `createVoteId` 方法，调用实际的API接口
   - 新增了 `updateVoteId` 方法，处理投票更新
   - 在 `confirmInsert` 方法中添加了投票数据验证和API调用逻辑
   - 优化了投票选项的添加和删除逻辑

### 数据转换

编辑器内部使用的数据格式会自动转换为API要求的格式：

- `type: "single"` → `vote_type: 1`
- `type: "multiple"` → `vote_type: 2`
- `options[].text` → `options[].name`
- `options[].value` → `options[].value`

## 测试

可以使用 `src/components/WysiwygEditor/vote-test.vue` 组件进行功能测试。

## 注意事项

1. 投票ID由后端API返回，前端不需要手动生成
2. 编辑投票时，投票ID字段会显示但不可编辑
3. 删除投票选项时，如果最大选择数超过剩余选项数，会自动调整
4. 投票类型改变时，最大选择数会自动调整
