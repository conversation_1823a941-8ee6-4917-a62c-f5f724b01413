# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
indent_style = space
indent_size = 4

# 4 space indentation
[*.py]
indent_style = space
indent_size = 4

# Tab indentation (no size specified)
[Makefile]
indent_style = space
indent_size = 4

# Indentation override for all JS under lib directory
[*.js]
indent_style = space
indent_size = 4

# Matches the exact files either package.json or .travis.yml
[{package.json, .travis.yml}]
indent_style = space
indent_size = 2
