<template>
  <div class="demo-container">
    <h2>所见即所得编辑器演示</h2>
    
    <div class="demo-section">
      <h3>编辑器</h3>
      <WysiwygEditor 
        v-model="content" 
        :height="600"
        @input="handleContentChange"
      />
    </div>
    
    <div class="demo-section">
      <h3>生成的Markdown内容</h3>
      <pre class="markdown-output">{{ content }}</pre>
    </div>
    
    <div class="demo-actions">
      <el-button @click="clearContent">清空内容</el-button>
      <el-button type="primary" @click="saveContent">保存内容</el-button>
      <el-button @click="loadSampleContent">加载示例内容</el-button>
    </div>
  </div>
</template>

<script>
import WysiwygEditor from './index.vue';

export default {
  name: 'WysiwygEditorDemo',
  components: {
    WysiwygEditor
  },
  data() {
    return {
      content: ''
    };
  },
  methods: {
    handleContentChange(newContent) {
      console.log('内容已更新:', newContent);
    },
    
    clearContent() {
      this.content = '';
    },
    
    saveContent() {
      if (this.content) {
        console.log('保存内容:', this.content);
        this.$message.success('内容已保存到控制台');
      } else {
        this.$message.warning('没有内容可保存');
      }
    },
    
    loadSampleContent() {
      this.content = `# 示例文章标题

这是一个示例文章，展示了所见即所得编辑器的功能。

## 二级标题

这里是一些文本内容，可以包含多行文本。
支持换行和基本的文本格式。

> 这是一个引用示例
> 
> —— 引用来源

### 列表示例

1. 第一个列表项
   - 子项1
   - 子项2
2. 第二个列表项
   - 子项A
   - 子项B
3. 第三个列表项

### 商品卡片示例

:::product-card
---
id: 12345
---
![商品图片](https://images.vinehoo.com/example.jpg "商品图片")
!! 精选葡萄酒
!!! Premium Wine Selection
**价格：** ¥299.99
**已售：** 1,234件
> 这是一款来自法国波尔多的精选红酒，口感醇厚，适合收藏。
:::

### 投票示例

:::vote[vote_123]{.single .deadline=2025-12-31}
# 你最喜欢的葡萄酒类型？

- [ ] 干红葡萄酒{value=red}
- [ ] 干白葡萄酒{value=white}
- [ ] 气泡酒{value=sparkling}
- [ ] 甜酒{value=sweet}
:::

### 表格示例

| 产区 | 葡萄品种 | 价格 |
|-----|---------|------|
| 波尔多 | 赤霞珠 | ¥299 |
| 勃艮第 | 黑皮诺 | ¥599 |
| 香槟 | 霞多丽 | ¥899 |

**日期：** 2025-01-08

![示例图片](https://images.vinehoo.com/sample.jpg "示例图片")`;
    }
  }
};
</script>

<style scoped lang="scss">
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  
  h3 {
    color: #409eff;
    margin-bottom: 15px;
    border-bottom: 2px solid #409eff;
    padding-bottom: 5px;
  }
}

.markdown-output {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.demo-actions {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #eee;
  
  .el-button {
    margin: 0 8px;
  }
}
</style>
