# 所见即所得编辑器 (WYSIWYG Editor)

一个功能丰富的所见即所得编辑器组件，专为葡萄酒内容管理系统设计。用户可以直接看到实际的展示效果，支持拖拽排序、实时编辑和删除操作。

## 核心特性

### 🎯 所见即所得
- 用户看到的是实际的展示效果，而不是Markdown代码
- 支持实时预览和编辑
- 自动生成标准Markdown格式用于后端存储

### 🔧 丰富的内容模块
1. **标题** - 支持1-3级标题，可视化显示
2. **子标题** - 快速插入子标题
3. **日期** - 支持多种日期格式显示
4. **文本框** - 支持多行文本和换行
5. **图片** - 集成vos-oss上传，支持描述和标注
6. **列表** - 有序列表支持无序子项嵌套
7. **商品卡片** - 专用的商品展示卡片
8. **引用** - 支持引用内容和来源
9. **投票** - 智能投票模块，自动获取后端ID
10. **表格** - 动态表格生成

### 🎨 交互体验
- **拖拽排序** - 支持内容块的拖拽重新排序
- **悬停操作** - 鼠标悬停显示编辑和删除按钮
- **实时编辑** - 双击或点击编辑按钮进行修改
- **一键删除** - 支持内容块的快速删除

### 🔄 智能功能
- **投票ID管理** - 新增投票时自动调用后端接口获取ID
- **嵌套列表** - 有序列表支持无序子项
- **图片上传** - 集成项目OSS上传功能
- **表单验证** - 完整的表单验证机制

## 使用方法

### 基本用法

```vue
<template>
  <div>
    <WysiwygEditor 
      v-model="content" 
      :height="500"
      @input="handleContentChange"
    />
  </div>
</template>

<script>
import WysiwygEditor from '@/components/WysiwygEditor';

export default {
  components: {
    WysiwygEditor
  },
  data() {
    return {
      content: '' // 存储生成的Markdown内容
    };
  },
  methods: {
    handleContentChange(newContent) {
      console.log('生成的Markdown:', newContent);
      // 这里可以处理内容变化，比如自动保存
    }
  }
};
</script>
```

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | '' | 编辑器内容（支持v-model） |
| height | Number | 400 | 编辑器最小高度（像素） |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | content: String | 内容变化时触发，返回生成的Markdown |

## 内容模块详解

### 列表模块
- **一级列表**：自动编号的有序列表
- **二级列表**：支持在有序列表项下添加无序子项
- **嵌套结构**：
  ```
  1. 第一项
     - 子项1
     - 子项2
  2. 第二项
     - 子项A
  ```

### 投票模块
- **新增投票**：不显示投票ID，点击确定后调用后端接口获取ID
- **编辑投票**：显示投票ID但不可编辑
- **投票类型**：支持单选和多选
- **截止日期**：可选的投票截止时间

### 商品卡片
- **商品信息**：ID、图片、中英文标题、价格、销量
- **可视化展示**：卡片式布局，直观展示商品信息
- **图片上传**：集成vos-oss组件

### 图片模块
- **上传功能**：使用项目中的vos-oss组件
- **图片信息**：支持alt描述和caption标注
- **响应式**：自动适应容器宽度

## 操作指南

### 添加内容
1. 点击工具栏中的相应按钮
2. 在弹出的配置窗口中填写内容
3. 点击确定，内容块将添加到编辑区域

### 编辑内容
1. 鼠标悬停在内容块上
2. 点击出现的"编辑"按钮
3. 在弹窗中修改内容
4. 点击确定保存修改

### 删除内容
1. 鼠标悬停在内容块上
2. 点击"删除"按钮
3. 确认删除操作

### 拖拽排序
1. 鼠标悬停在内容块上
2. 拖拽左侧的网格图标
3. 将内容块拖拽到目标位置

## 生成的Markdown格式

编辑器会自动生成标准的Markdown格式，包括扩展语法：

### 投票语法
```markdown
:::vote[投票ID]{.single .deadline=2025-12-31}
# 投票标题

- [ ] 选项1{value=option1}
- [ ] 选项2{value=option2}
:::
```

### 商品卡片语法
```markdown
:::product-card
---
id: 12345
---
![商品图片](图片URL "alt文字")
!! 商品中文标题
!!! 商品英文标题
**价格：** ¥199.99
**已售：** 1,234件
> 商品描述
:::
```

### 嵌套列表语法
```markdown
1. 第一项
   - 子项1
   - 子项2
2. 第二项
   - 子项A
```

## 技术实现

### 依赖
- **Vue 2.x** - 框架
- **Element UI** - UI组件库
- **vuedraggable** - 拖拽排序功能
- **vos-oss** - 图片上传组件

### 架构
- **组件化设计** - 每种内容类型都有独立的渲染组件
- **数据驱动** - 使用contentBlocks数组管理所有内容块
- **双向绑定** - 支持v-model，自动同步Markdown内容

### 扩展性
- **新增内容类型** - 在blocks/index.js中注册新组件
- **自定义样式** - 通过SCSS变量自定义外观
- **API集成** - 支持自定义后端接口调用

## 注意事项

1. **投票接口** - 需要实现`this.$request.vote.create`接口
2. **图片上传** - 确保vos-oss组件配置正确
3. **浏览器兼容** - 支持现代浏览器，IE需要polyfill
4. **性能优化** - 大量内容块时建议启用虚拟滚动

## 示例

查看 `demo.vue` 文件获取完整的使用示例和效果预览。
