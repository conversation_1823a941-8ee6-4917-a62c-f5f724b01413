<template>
    <div class="vote-test">
        <h2>投票功能测试</h2>
        <el-button @click="testCreateVote" type="primary">测试创建投票</el-button>
        <el-button @click="testUpdateVote" type="success">测试更新投票</el-button>
        
        <div v-if="testResult" class="test-result">
            <h3>测试结果：</h3>
            <pre>{{ testResult }}</pre>
        </div>
    </div>
</template>

<script>
export default {
    name: "VoteTest",
    data() {
        return {
            testResult: null,
            testVoteId: null
        };
    },
    methods: {
        async testCreateVote() {
            try {
                const testData = {
                    title: "测试多选投票标题",
                    vote_type: 2, // 多选
                    max_choices: 2,
                    options: [
                        {
                            name: "选项1",
                            value: "option1"
                        },
                        {
                            name: "选项2", 
                            value: "option2"
                        },
                        {
                            name: "选项3",
                            value: "option3"
                        }
                    ]
                };

                const response = await this.$request.winesmel.createVote(testData);
                
                if (response.data.error_code === 0) {
                    this.testVoteId = response.data.data.vote_id;
                    this.testResult = {
                        success: true,
                        message: "创建投票成功",
                        data: response.data.data
                    };
                } else {
                    this.testResult = {
                        success: false,
                        message: "创建投票失败",
                        error: response.data
                    };
                }
            } catch (error) {
                this.testResult = {
                    success: false,
                    message: "创建投票异常",
                    error: error.message
                };
            }
        },

        async testUpdateVote() {
            if (!this.testVoteId) {
                this.$message.warning("请先创建投票");
                return;
            }

            try {
                const testData = {
                    vote_id: parseInt(this.testVoteId),
                    title: "修改后 - 测试多选投票标题",
                    vote_type: 2, // 多选
                    max_choices: 3,
                    options: [
                        {
                            name: "修改后选项1",
                            value: "option1_updated"
                        },
                        {
                            name: "修改后选项2",
                            value: "option2_updated"
                        },
                        {
                            name: "新增选项3",
                            value: "option3_new"
                        }
                    ]
                };

                const response = await this.$request.winesmel.updateVote(testData);
                
                if (response.data.error_code === 0) {
                    this.testResult = {
                        success: true,
                        message: "更新投票成功",
                        data: response.data
                    };
                } else {
                    this.testResult = {
                        success: false,
                        message: "更新投票失败",
                        error: response.data
                    };
                }
            } catch (error) {
                this.testResult = {
                    success: false,
                    message: "更新投票异常",
                    error: error.message
                };
            }
        }
    }
};
</script>

<style scoped>
.vote-test {
    padding: 20px;
}

.test-result {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.test-result pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
