<template>
  <div class="test-container">
    <h2>编辑器测试页面</h2>
    
    <div class="test-section">
      <h3>编辑器</h3>
      <WysiwygEditor 
        v-model="content" 
        :height="600"
      />
    </div>
    
    <div class="test-section">
      <h3>当前内容</h3>
      <pre>{{ content }}</pre>
    </div>
    
    <div class="test-actions">
      <el-button @click="loadTestContent">加载测试内容</el-button>
      <el-button @click="clearContent">清空内容</el-button>
    </div>
  </div>
</template>

<script>
import WysiwygEditor from './index.vue';

export default {
  name: 'WysiwygEditorTest',
  components: {
    WysiwygEditor
  },
  data() {
    return {
      content: `# 测试标题

这是一段测试文本内容，用来验证编辑器的解析功能。
支持多行文本显示。

## 二级标题

> 这是一个引用示例
> 
> —— 引用来源

### 列表示例

1. 第一个列表项
   - 子项1
   - 子项2
2. 第二个列表项
   - 子项A

**日期：** 2025-01-08

![测试图片](https://images.vinehoo.com/test.jpg "测试图片")

| 产区 | 品种 | 价格 |
|-----|------|------|
| 波尔多 | 赤霞珠 | ¥299 |
| 勃艮第 | 黑皮诺 | ¥599 |

:::vote[test123]{.single .deadline=2025-12-31}
# 你最喜欢的葡萄酒类型？

- [ ] 干红葡萄酒{value=red}
- [ ] 干白葡萄酒{value=white}
- [ ] 气泡酒{value=sparkling}
:::`
    };
  },
  methods: {
    loadTestContent() {
      this.content = `# 新的测试内容

这是重新加载的测试内容。

## 功能测试

> 测试引用功能
> 
> —— 测试来源

1. 测试列表项1
   - 子项测试1
   - 子项测试2
2. 测试列表项2

**日期：** 2025-01-09`;
    },
    
    clearContent() {
      this.content = '';
    }
  }
};
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  color: #409eff;
  margin-bottom: 15px;
}

.test-actions {
  text-align: center;
  padding: 20px 0;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
}
</style>
