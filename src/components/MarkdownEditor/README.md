# Markdown编辑器组件

一个功能丰富的Markdown编辑器组件，专为葡萄酒内容管理系统设计，支持多种内容模块的插入和编辑。

## 功能特性

### 支持的内容模块

1. **标题** - 支持一级、二级、三级标题
2. **子标题** - 快速插入子标题
3. **日期** - 支持多种日期格式
4. **文本框** - 普通文本内容
5. **图片** - 集成vos-oss上传，支持图片描述和标注
6. **列表** - 支持有序和无序列表
7. **商品卡片** - 专用的商品展示卡片
8. **引用** - 支持引用内容和来源
9. **投票** - 支持单选/多选投票模块
10. **表格** - 动态表格生成

### 特殊语法支持

#### 投票模块
```markdown
:::vote[投票ID]{.single .deadline=2025-12-31}
# 投票标题

- [ ] 选项1{value=option1}
- [ ] 选项2{value=option2}
- [ ] 选项3{value=option3}
:::
```

#### 商品卡片
```markdown
:::product-card
---
id: 12345
---
![商品图片](图片URL "alt文字")
!! 商品中文标题
!!! 商品英文标题
**价格：** ¥199.99
**已售：** 1,234件
> 商品描述
:::
```

## 使用方法

### 基本用法

```vue
<template>
  <div>
    <MarkdownEditor 
      v-model="content" 
      :height="400"
      @input="handleContentChange"
    />
  </div>
</template>

<script>
import MarkdownEditor from '@/components/MarkdownEditor';

export default {
  components: {
    MarkdownEditor
  },
  data() {
    return {
      content: ''
    };
  },
  methods: {
    handleContentChange(newContent) {
      console.log('内容更新:', newContent);
    }
  }
};
</script>
```

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | '' | 编辑器内容（支持v-model） |
| height | Number | 400 | 编辑器高度（像素） |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | content: String | 内容变化时触发 |

## 工具栏功能

### 标题模块
- 支持1-3级标题
- 弹窗配置标题内容和级别

### 图片模块
- 集成vos-oss上传组件
- 支持图片描述和标注
- 自动生成markdown图片语法

### 列表模块
- 支持有序列表和无序列表
- 动态添加/删除列表项
- 自动生成markdown列表语法

### 商品卡片模块
- 商品ID配置
- 商品图片上传
- 中英文标题
- 价格和销量信息
- 商品描述

### 投票模块
- 投票ID和标题配置
- 单选/多选类型
- 截止日期设置
- 动态选项管理
- 自动生成扩展语法

### 表格模块
- 动态行列数配置
- 表头自定义
- 自动生成markdown表格

## 样式定制

组件使用SCSS编写样式，支持以下自定义：

```scss
.markdown-editor {
  // 编辑器容器样式
  
  .editor-toolbar {
    // 工具栏样式
  }
  
  .editor-content {
    // 编辑区域样式
  }
  
  .editor-preview {
    // 预览区域样式（如果启用）
  }
}
```

## 依赖

- Element UI - UI组件库
- vos-oss - 图片上传组件
- Vue 2.x - 框架版本

## 注意事项

1. 确保项目中已安装并配置了vos-oss组件
2. 图片上传需要正确配置OSS相关参数
3. 生成的markdown内容包含扩展语法，需要相应的解析器支持
4. 建议在表单验证中包含内容长度和格式检查

## 示例

查看 `demo.vue` 文件获取完整的使用示例和效果预览。
