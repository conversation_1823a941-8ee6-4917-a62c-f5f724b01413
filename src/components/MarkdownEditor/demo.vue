<template>
  <div class="demo-container">
    <h2>Markdown编辑器演示</h2>
    
    <div class="demo-section">
      <h3>编辑器</h3>
      <MarkdownEditor 
        v-model="content" 
        :height="500"
        @input="handleContentChange"
      />
    </div>
    
    <div class="demo-section">
      <h3>生成的Markdown内容</h3>
      <pre class="markdown-output">{{ content }}</pre>
    </div>
    
    <div class="demo-section">
      <h3>预览效果</h3>
      <div class="preview-area" v-html="previewHtml"></div>
    </div>
  </div>
</template>

<script>
import MarkdownEditor from './index.vue';

export default {
  name: 'MarkdownEditorDemo',
  components: {
    MarkdownEditor
  },
  data() {
    return {
      content: `# 示例文章标题

这是一个示例文章，展示了Markdown编辑器的功能。

## 二级标题

这里是一些文本内容。

> 这是一个引用示例
> 
> —— 引用来源

### 列表示例

- 列表项1
- 列表项2
- 列表项3

### 商品卡片示例

:::product-card
---
id: 12345
---
![商品图片](https://images.vinehoo.com/example.jpg "商品图片")
!! 商品中文标题
!!! Product English Title
**价格：** ¥199.99
**已售：** 1,234件
> 这是商品描述信息
:::

### 投票示例

:::vote[123]{.single .deadline=2025-12-31}
# 你最喜欢的葡萄酒类型？

- [ ] 干红{value=red}
- [ ] 干白{value=white}
- [ ] 气泡酒{value=sparkling}
:::

### 表格示例

| 产区 | 葡萄品种 | 价格 |
|-----|---------|------|
| 波尔多 | 赤霞珠 | ¥299 |
| 勃艮第 | 黑皮诺 | ¥599 |
| 香槟 | 霞多丽 | ¥899 |`
    };
  },
  computed: {
    previewHtml() {
      // 简单的markdown转HTML预览
      let html = this.content;
      
      // 处理标题
      html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
      html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
      html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
      
      // 处理粗体
      html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      
      // 处理列表
      html = html.replace(/^\- (.*$)/gim, '<li>$1</li>');
      html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
      
      // 处理引用
      html = html.replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>');
      
      // 处理换行
      html = html.replace(/\n/g, '<br>');
      
      return html;
    }
  },
  methods: {
    handleContentChange(newContent) {
      console.log('内容已更新:', newContent);
    }
  }
};
</script>

<style scoped lang="scss">
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  
  h3 {
    color: #409eff;
    margin-bottom: 15px;
    border-bottom: 2px solid #409eff;
    padding-bottom: 5px;
  }
}

.markdown-output {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.preview-area {
  background: #fff;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-height: 200px;
  
  h1, h2, h3 {
    color: #333;
    margin: 10px 0;
  }
  
  h1 {
    font-size: 24px;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
  }
  
  h2 {
    font-size: 20px;
    color: #666;
  }
  
  h3 {
    font-size: 16px;
    color: #888;
  }
  
  blockquote {
    border-left: 4px solid #ddd;
    margin: 10px 0;
    padding-left: 15px;
    color: #666;
    font-style: italic;
  }
  
  ul {
    padding-left: 20px;
    
    li {
      margin: 5px 0;
    }
  }
  
  strong {
    font-weight: bold;
    color: #333;
  }
}
</style>
