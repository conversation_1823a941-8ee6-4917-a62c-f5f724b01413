<template>
    <div class="upload-container">
        <el-button
            :disabled="readonly"
            :style="{ background: color, borderColor: color }"
            icon="el-icon-upload"
            size="mini"
            type="primary"
            @click="dialogVisible = true"
        >
            上传
        </el-button>
        <el-dialog
            :visible.sync="dialogVisible"
            append-to-body
            :close-on-click-modal="false"
        >
            <div
                style="margin-bottom: 20px; text-align: center"
                v-if="dialogVisible"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :dir="dir"
                    :file-list="fileList"
                    :multiple="true"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </div>
            <!-- <UploadImage ref="UploadImage" v-model="entity.product_img1"></UploadImage> -->
            <!-- <el-upload
        :multiple="true"
        :file-list="fileList"
        :show-file-list="true"
        :on-remove="handleRemove"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        class="editor-slide-upload"
        :http-request="uploadImg"
        list-type="picture-card"
        action
      >
        <el-button size="small" type="primary">
          点击上传
        </el-button>
      </el-upload> -->
            <el-button @click="dialogVisible = false"> 取消 </el-button>
            <el-button type="primary" @click="handleSubmit"> 确定 </el-button>
        </el-dialog>
    </div>
</template>

<script>
// import { getToken } from 'api/qiniu'
import vosOss from "vos-oss";
// import OSS from 'ali-oss';
export default {
    name: "EditorSlideUpload",
    components: {
        vosOss,
    },
    props: {
        color: {
            type: String,
            default: "#1890ff",
        },
        // eslint-disable-next-line vue/require-prop-type-constructor
        readonly: false,
    },
    data() {
        return {
            dialogVisible: false,
            listObj: {},
            fileList: [],
            uploadurl: "",
            dir: "vinehoo/tinymce/",
            oss_url: "",
        };
    },
    mounted() {
        this.uploadurl = process.env.VUE_APP_BASE_UPLOAD + "/image";
        if (process.env.NODE_ENV == "development") {
            this.oss_url = "https://images.wineyun.com";
        } else {
            this.oss_url = "https://images.vinehoo.com";
        }
        console.warn(this.oss_url);
    },
    methods: {
        checkAllSuccess() {
            return Object.keys(this.listObj).every(
                (item) => this.listObj[item].hasSuccess
            );
        },
        handleSubmit() {
            let urlList = [];
            this.fileList.map((item) => {
                console.warn(this.oss_url + item);
                urlList.push(this.oss_url + item);
            });
            console.warn(this.fileList, 333);
            this.$emit("successCBK", urlList);
            this.listObj = {};
            urlList = [];
            this.fileList = [];
            this.dialogVisible = false;
        },
        handleRemove(file) {
            this.fileList.forEach((item, index) => {
                if (item.uid == file.uid) {
                    this.fileList.splice(index, 1);
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.editor-slide-upload {
    margin-bottom: 20px;

    /deep/ .el-upload--picture-card {
        width: 100%;
    }
}
</style>
