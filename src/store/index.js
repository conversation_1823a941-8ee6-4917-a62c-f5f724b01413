import Vue from "vue";
import Vuex from "vuex";
Vue.use(Vuex);
const state = {
    routesList: []
};

const getters = {
    routesList: state => state.routesList
};
const mutations = {
    setRoutesList(state, value) {
        state.routesList = value;
    }
};
const actions = {
    setRoutesList: (event, value) => {
        event.commit("setRoutesList", value);
    }
};

const store = new Vuex.Store({
    state,
    getters,
    mutations,
    actions
});
export default store;
