<template>
    <div>
        <el-form ref="form" :model="datas" label-width="100px" :rules="rules">
            <el-form-item label="马甲账号" prop="vuid">
                <el-col :span="12">
                    <el-select
                        v-model="datas.vuid"
                        clearable
                        placeholder="请选择马甲账号"
                        class="filter-item"
                    >
                        <el-option
                            v-for="item in vuidOption"
                            :key="item.uid"
                            :label="
                                '' + item.user_level + '级-' + item.nickname
                            "
                            :value="item.uid"
                        />
                    </el-select>
                </el-col>
            </el-form-item>
            <el-form-item label="酒闻ID" prop="id">
                <el-col :span="12">
                    <el-input v-model="datas.id" placeholder="请输入酒闻ID" />
                </el-col>
            </el-form-item>
            <el-form-item label="评论内容" prop="content">
                <el-col :span="12">
                    <el-input
                        v-model="datas.content"
                        placeholder="请输入评论内容"
                        type="textarea"
                        :autosize="{ minRows: 4 }"
                    />
                </el-col>
            </el-form-item>
        </el-form>
        <div class="dialog-footer">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="submits">确 定</el-button>
        </div>
    </div>
</template>
<script>
export default {
    props: ["CommentDetail"],
    data() {
        return {
            datas: {
                vuid: "",
                content: ""
            },
            rules: {
                id: [
                    {
                        required: true,
                        message: "请输入酒闻ID",
                        trigger: "change"
                    }
                ],
                vuid: [
                    {
                        required: true,
                        message: "请选择马甲账号",
                        trigger: "change"
                    }
                ],
                content: [
                    {
                        required: true,
                        message: "请输入评论内容",
                        trigger: "blur"
                    }
                ]
            },
            vuidOption: []
        };
    },
    mounted() {
        this.getVestList();
    },
    methods: {
        async getVestList() {
            const res = await this.$request.winesmel.getVestList();
            if (res.data.error_code == 0) {
                this.vuidOption = res.data.data.list;
            }
        },
        //提交
        async submits() {
            if (this.validateForm()) {
                const data = {
                    ...this.datas
                };

                this.$request.winesmel.sendMakeCommentOn(data).then(res => {
                    if (res.data.error_code == 0) {
                        this.$emit("close");
                        this.$message.success("评论成功");
                    }
                });
            }
        },
        validateForm() {
            let flag = null;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        }
    }
};
</script>
<style scoped lang="scss">
.dialog-footer {
    display: flex;
    justify-content: center;
}
.el-upload-list .is-ready {
    display: none;
}
</style>
