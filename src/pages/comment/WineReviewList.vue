<template>
    <div>
        <el-card shadow="hover">
            <!-- 高级查询 -->
            <el-form :inline="true" size="small" class="demo-form-inline">
                <el-form-item label="">
                    <el-input
                        size="mini"
                        clearable
                        v-model="query.keywords"
                        placeholder="评论内容"
                        @keyup.enter.native="search"
                    />
                </el-form-item>
                <el-form-item label="">
                    <el-input
                        v-model="query.aid"
                        size="mini"
                        clearable
                        placeholder="请输入酒闻ID"
                        @keyup.enter.native="search"
                    />
                </el-form-item>
                <el-form-item label="">
                    <el-input
                        clearable
                        v-model="query.uid"
                        size="mini"
                        @keyup.enter.native="search"
                        placeholder="请输入用户ID"
                    />
                </el-form-item>
                <el-form-item label="">
                    <el-select
                        v-model="query.audit_status"
                        clearable
                        class="w-mini"
                        size="mini"
                        placeholder="审核状态"
                    >
                        <el-option
                            v-for="(item, index) in auditStatusOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="warning" @click="search" size="mini"
                        >查询</el-button
                    >
                    <el-button
                        type="success"
                        @click="addDialogStatus = true"
                        size="mini"
                        >发布评论</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover">
            <el-table
                stripe
                border
                size="mini"
                :data="tableList"
                style="width: 100%;"
            >
                <el-table-column
                    label="文章标题" 
                    prop="title"
                    show-overflow-tooltip
                    min-width="180"
                    align="left"
                >
                    <template slot-scope="{ row }">
                        <el-link
                            type="primary"
                            :href="`https://vinehoo.com/wineSmell/wineSmellDetail?id=${row.aid}`"
                            target="_blank"
                            :underline="false"
                            style="text-decoration: none;"
                        >
                            {{ row.title }}
                        </el-link>
                    </template>
                </el-table-column>

                <el-table-column
                    label="评论内容"
                    prop="content"
                    min-width="250"
                    align="center"
                    show-overflow-tooltip
                >
                    <template slot-scope="{ row }">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <img
                                :src="row.avatar_image"
                                style="width: 30px; height: 30px; border-radius: 50%;"
                            />
                            <div style="text-align: left;">
                                <div style="display: flex; align-items: center; gap: 4px;">
                                    <div style="font-weight: bold;">{{ row.nickname }}</div>
                                    <el-tag 
                                        size="mini" 
                                        type="info"
                                        @click="copyText(row.comid)"
                                        style="cursor: pointer;"
                                    >评论ID: {{ row.comid }}</el-tag>
                                    <el-tag 
                                        size="mini" 
                                        type="info"
                                        @click="copyText(row.uid)"
                                        style="cursor: pointer;"
                                    >用户ID: {{ row.uid }}</el-tag>
                                    <el-tag 
                                        size="mini" 
                                        type="info" 
                                        v-if="row.pid"
                                        @click="copyText(row.pid)"
                                        style="cursor: pointer;"
                                    >回复ID: {{ row.pid }}</el-tag>
                                </div>
                                <div style="display: flex; align-items: center; gap: 4px;">
                                    <el-tag
                                        size="mini"
                                        :type="row.audit_status === 2 ? 'success' : row.audit_status === 3 ? 'danger' : 'info'"
                                    >
                                        {{ row.audit_status | audit_status }}
                                    </el-tag>
                                    <div>{{ row.content }}</div>
                                </div>
                                <div>{{ row.emoji_image }}</div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="热度"
                    prop="hot_vaule"
                    width="100"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <el-input
                            size="mini"
                            v-model="row.hot_vaule"
                            oninput="value=value.replace(/[^0-9]/g,'')"
                            @change="updateHotNum(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column
                    label="评论时间"
                    prop="addtime"
                    align="center"
                    width="150"
                />

                <el-table-column label="操作" fixed="right" align="center" width="180">
                    <template slot-scope="row">
                        <el-button
                            size="mini"
                            @click="replyComment(row.row)"
                            type="text"
                        >
                            回复
                        </el-button>
                        <el-button
                            @click="setCommentStatus(row.row, 4)"
                            size="mini"
                            v-if="
                                row.row.audit_status == 2 ||
                                    row.row.audit_status == 3
                            "
                            type="text"
                            >不推荐</el-button
                        >
                        <el-button
                            size="mini"
                            @click="setCommentStatus(row.row, 1)"
                            v-if="
                                row.row.audit_status == 3 ||
                                    row.row.audit_status == 4
                            "
                            type="text"
                            >通过</el-button
                        >
                        <!-- @click="setCommentStatus(row.row, 2)" -->

                        <el-button
                            size="mini"
                            @click="rejectComment(row.row, 2)"
                            v-if="
                                row.row.audit_status == 2 ||
                                    row.row.audit_status == 4
                            "
                            type="text"
                            >驳回</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="flex-layout">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="pageSize"
                :current-page="currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <el-dialog
            :visible.sync="replyDialogStatus"
            title="回复评论"
            width="630px"
        >
            <replyComment
                @close="close"
                :replyCommentData="replyCommentData"
                v-if="replyDialogStatus"
            ></replyComment>
        </el-dialog>
        <el-dialog
            :visible.sync="addDialogStatus"
            title="添加评论"
            width="630px"
        >
            <addComment @close="close" v-if="addDialogStatus"></addComment>
        </el-dialog>
    </div>
</template>

<script>
import replyComment from "./replyComment.vue";
import addComment from "./addComment.vue";
export default {
    components: {
        addComment,
        replyComment
    },
    data() {
        return {
            replyDialogStatus: false,
            addDialogStatus: false,
            currentPage: 1, // 当前页
            pageSize: 10, // 每页条数
            total: 0, // 总条数
            replyCommentData: {},
            query: {
                keywords: "",
                aid: "",
                uid: "",
                audit_status: ""
            },
            tableList: [],
            auditStatusOptions: [
                {
                    label: "已通过",
                    value: "2"
                },
                {
                    label: "已驳回",
                    value: "3"
                },
                {
                    label: "不推荐",
                    value: "4"
                }
            ]
        };
    },
    filters: {
        audit_status(val) {
            switch (val) {
                case 1:
                    return "待审核";
                case 2:
                    return "已通过";
                case 3:
                    return "已驳回";
                case 4:
                    return "不推荐";
                default:
                    return "未知";
            }
        }
    },
    mounted() {
        this.getCommentList();
    },
    methods: {
        copyText(text) {
            navigator.clipboard.writeText(text).then(() => {
                this.$message.success('复制成功');
            }).catch(() => {
                this.$message.error('复制失败');
            });
        },
        rejectComment(row, status) {
            this.$prompt("请输入驳回原因", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消"
            }).then(async ({ value }) => {
                if (!value) {
                    this.$message.warning("请输入驳回原因");
                    return;
                }
                const data = {
                    id: row.comid,
                    status: status,
                    source: 5,
                    type: row.pid ? 2 : 1,
                    remark: value
                };
                const res = await this.$request.winesmel.setCommentStatus(data);
                if (res.data.error_code == 0) {
                    this.$message.success("操作成功");
                    this.getCommentList();
                }
            });
        },
        async setCommentStatus(row, status) {
            const data = {
                id: row.comid,
                source: 5,
                type: row.pid ? 2 : 1,
                status
            };
            const res = await this.$request.winesmel.setCommentStatus(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getCommentList();
            }
            console.log(data);
        },
        async updateHotNum(row) {
            const data = {
                comid: row.comid,
                hot_vaule: row.hot_vaule
            };
            console.log(data);
            const res = await this.$request.winesmel.upcommentHot(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getCommentList();
            }
        },
        replyComment(row) {
            this.replyDialogStatus = true;
            this.replyCommentData = row;
        },
        getCommentList() {
            const params = {
                ...this.query,
                page: this.currentPage,
                limit: this.pageSize
            };
            // if (params.is_show === "") {
            //     delete params.is_show;
            // }
            this.$request.winesmel.getWineSmellComment(params).then(res => {
                if (res.data.error_code == 0) {
                    this.tableList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        close() {
            this.addDialogStatus = false;
            this.replyDialogStatus = false;
            this.getCommentList();
        },
        // 高级查询
        search() {
            this.currentPage = 1;
            this.getCommentList();
        },
        // 改变每页条数
        handleSizeChange(val) {
            this.currentPage = 1;
            this.pageSize = val;
            this.getCommentList();
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getCommentList();
        }
    }
};
</script>

<style lang="scss" scoped>
.flex-layout {
    display: flex;
    justify-content: center;
}
</style>
