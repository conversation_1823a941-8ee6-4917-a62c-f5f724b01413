<template>
    <div>
        <el-card shadow="hover">
            <!-- 高级查询 -->
            <!-- <el-form :inline="true" size="small"> -->
            <!-- <el-form-item label="">
                    <el-input
                        v-model="query.keywords"
                        size="mini"
                        placeholder="类型名称"
                    />
                </el-form-item> -->

            <!-- <el-form-item> -->
            <!-- <el-button size="mini" type="success" @click="search"
                        >查询</el-button
                    > -->
            <el-button type="primary" @click="dialogVisible = true" size="mini"
                >添加类型</el-button
            >
            <!-- </el-form-item> -->
            <!-- </el-form> -->
        </el-card>
        <el-card shadow="hover">
            <el-table
                stripe
                border
                size="mini"
                :data="DataList"
                style="width: 100%;"
            >
                <el-table-column label="类型ID" prop="id" align="center" />
                <el-table-column label="类型名称" prop="name" align="center" />
                <el-table-column
                    label="创建时间"
                    prop="create_time"
                    align="center"
                />
                <el-table-column label="操作" fixed="right" align="center" width="160">
                    <template slot-scope="{ row }">
                        <el-button type="warning" size="mini" @click="edit(row)"
                            >编辑</el-button
                        >

                        <el-button
                            type="danger"
                            @click="deleteRow(row)"
                            size="mini"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <el-dialog
            :visible.sync="dialogVisibleEdit"
            title="编辑类型"
            :before-close="close"
            height="50%"
            top="50px"
        >
            <edit
                v-if="dialogVisibleEdit"
                :detali="detali"
                @close="close"
            ></edit>
        </el-dialog>
        <el-dialog
            :visible.sync="dialogVisible"
            title="新增类型"
            height="50%"
            top="50px"
        >
            <operations
                v-if="dialogVisible"
                @close="close"
                ref="operation"
            ></operations>
        </el-dialog>
    </div>
</template>

<script>
import operations from "./WineSmellTypeOp.vue";
import edit from "./WineSmellTypeEdit.vue";
export default {
    components: {
        edit,
        operations
    },
    name: "virtualOrder",
    data() {
        return {
            currentPage: 1, // 当前页
            dialogVisibleEdit: false,
            pageSize: 10, // 每页条数
            total: 0, // 总条数
            detali: {},
            query: {
                keywords: "",
                cate_id: "",
                status: "",
                stort: "",
                id: ""
            },
            DataList: [],
            ids: [],
            dialogVisible: false,
            cateOption: [],
            loading: false,
            sourcetxt: {
                1: "官方酒会",
                2: "展会",
                3: "其他酒会"
            },
            expands: [],
            stortOption: [
                {
                    label: "浏览量",
                    value: 1
                },
                {
                    label: "状态",
                    value: 2
                },
                {
                    label: "更新时间 ",
                    value: 3
                }
            ],
            statusOption: [
                {
                    label: "已审核",
                    value: 1
                },
                {
                    label: "待审核",
                    value: 2
                }
            ]
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        async deleteRow(row) {
            const data = {
                id: row.id
            };
            const res = await this.$request.winesmel.deleteArticleType(data);
            if (res.data.error_code == 0) {
                this.getData();
                this.$message.success("操作成功");
            }
        },
        close() {
            this.dialogVisibleEdit = false;
            this.dialogVisible = false;
            this.getData();
        },
        async getData() {
            const res = await this.$request.winesmel.getArticleTypeListTable();
            if (res.data.error_code == 0) {
                console.log(res.data.data);
                this.DataList = res.data.data.list;
            }
        },
        // 编辑
        edit(row) {
            this.detali = row;
            this.dialogVisibleEdit = true;
        }
    }
};
</script>

<style lang="scss" scoped></style>
