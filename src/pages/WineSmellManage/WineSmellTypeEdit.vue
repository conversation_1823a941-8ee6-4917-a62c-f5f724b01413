<template>
    <el-form ref="form" :model="form" label-width="100px" :rules="rules">
        <el-form-item label="类型名称" prop="name">
            <el-col :span="12">
                <el-input v-model="form.name" placeholder="请输入类型名称" />
            </el-col>
        </el-form-item>
        <div class="dialog-footer">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="submits('form')">确 定</el-button>
        </div>
    </el-form>
</template>
<script>
export default {
    props: ["detali"],
    data() {
        return {
            form: {
                name: "",
                id: ""
            },
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入类型名称",
                        trigger: "blur"
                    },
                    {
                        min: 1,
                        max: 8,
                        message: "类型名称长度在 1 到 8 个字符"
                    }
                ]
            }
        };
    },
    mounted() {
        this.form = this.detali;
    },
    methods: {
        async submits(ruleForm) {
            this.$refs[ruleForm].validate(async valid => {
                if (valid) {
                    const data = {
                        ...this.form
                    };
                    const res = await this.$request.winesmel.editArticleType(
                        data
                    );
                    if (res.data.error_code == 0) {
                        this.$emit("close");
                        this.$message.success("操作成功");
                    }
                }
            });
        }
    }
};
</script>
<style scoped lang="scss">
.dialog-footer {
    display: flex;
    justify-content: center;
}
</style>
