<template>
    <div>
        <el-card shadow="hover" style="margin: 0 0 10px;padding: 0 40px 0 0">
            <!-- 高级查询 -->
            <el-form :inline="true" size="small" class="demo-form-inline">
                <el-form-item>
                    <el-input
                        size="mini"
                        v-model="query.keywords"
                        placeholder="酒闻标题"
                        @keyup.enter.native="search"
                    />
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.cate_id"
                        clearable
                        size="mini"
                        placeholder="内容类型"
                        class="filter-item"
                        style="width: 140px"
                        @change="queryStatus"
                    >
                        <el-option
                            v-for="item in cateOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.status"
                        clearable
                        size="mini"
                        placeholder="状态"
                        class="filter-item"
                        style="width: 90px"
                        @change="queryStatus"
                    >
                        <el-option
                            v-for="item in statusOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.stort"
                        clearable
                        size="mini"
                        placeholder="排序"
                        class="filter-item"
                        style="width: 120px"
                        @change="queryStatus"
                    >
                        <el-option
                            v-for="item in sortOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button size="mini" type="success" @click="search"
                        >查询</el-button
                    >
                    <el-button
                        type="primary"
                        size="mini"
                        @click="handleClick('')"
                        >添加酒闻</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover">
            <el-table
                stripe
                border
                size="mini"
                :data="DataList"
                fit
                highlight-current-row
                style="width: 100%;"
            >
                <el-table-column
                    label="内容类型"
                    prop="catename"
                    width="100"
                    align="center"
                />
                <el-table-column label="状态" align="center" width="80">
                    <template slot-scope="{ row }">{{
                        row.status == 1 ? "展示中" : "未展示"
                    }}</template>
                </el-table-column>
                <el-table-column
                    label="文章标题"
                    show-overflow-tooltip
                    align="left"
                    min-width="200"
                >
                    <template slot-scope="{ row }">
                        <div style="display: flex; align-items: flex-start;">
                            <div style="margin-right: 10px">
                                <img
                                    :src="row.img"
                                    v-if="row.img"
                                    style="width: 50px;height: 50px; cursor:pointer"
                                    @click="showLargeImage(row.img)"
                                />
                            </div>
                            <div style="flex: 1">
                                <div>
                                    <a :href="'https://vinehoo.com/wineSmell/wineSmellDetail?id=' + row.id" 
                                       target="_blank" 
                                       style="color: #606266; text-decoration: none; cursor: pointer;"
                                       class="clickable-link">{{ row.title }}</a>
                                </div>
                                <div style="margin-top: 5px">
                                    <el-tag size="mini" type="info" v-if="row.topic_name">话题：{{ row.topic_name }}</el-tag>
                                    <template v-if="row.adname !== row.update_name">
                                        <el-tag size="mini" type="info" style="margin-left: 5px">创建人：{{ row.adname }}</el-tag>
                                    </template>
                                    <el-tag size="mini" type="info" style="margin-left: 5px">更新人：{{ row.update_name === '测试人员' ? '系统' : row.update_name }}</el-tag>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="浏览次数"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        <el-tooltip :content="row.viewnums + ''" placement="top">
                            <span>{{ formatNumber(row.viewnums) }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                
                <el-table-column label="排序" align="center" width="80">
                    <template slot-scope="{ row }">
                        <el-input
                            v-model="row.ordid"
                            size="mini"
                            oninput="value=value.replace(/[^\d]/g,'')"
                            @blur="updateSort(row)"
                        />
                    </template>
                </el-table-column>

                <el-table-column
                    label="更新时间"
                    align="center"
                    prop="updatetime"
                    width="160"
                />
                <el-table-column label="操作" fixed="right" align="center" width="180">
                    <template slot-scope="{ row }">
                        <el-button
                            type="warning"
                            size="mini"
                            @click="handleClick(row)"
                            >编辑
                        </el-button>
                        <el-button
                            :type="row.status == 1 ? 'info' : 'primary'"
                            size="mini"
                            @click="changeEnabled(row, row.status == 1 ? 0 : 1)"
                            >{{ row.status == 1 ? "下线" : "上线" }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <el-pagination
            style="margin-top: 10px;text-align: center;"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="pageSize"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        <el-dialog
            :before-close="handleClose"
            :visible.sync="dialogStatus"
            title="基本信息"
            custom-class="dialogwid"
            width="60%"
            height="50%"
            top="50px"
        >
            <WineSmellOp
                v-if="dialogStatus"
                ref="editForm"
                :parentObj="this"
                @close="close"
            ></WineSmellOp>
        </el-dialog>
        <el-dialog :visible.sync="dialogVisible1">
            <img width="100%" :src="imgUrl" alt="" />
        </el-dialog>
    </div>
</template>

<script>
import WineSmellOp from "./WineSmellOp.vue";
export default {
    components: {
        WineSmellOp
    },
    name: "virtualOrder",
    data() {
        return {
            dialogStatus: false,
            currentPage: 1, // 当前页
            pageSize: 10, // 每页条数
            total: 0, // 总条数
            query: {
                keywords: "",
                cate_id: "",
                status: "",
                stort: "",
                id: ""
            },
            DataList: [],
            dialogVisible: false,
            dialogVisible1: false,
            cateOption: [],
            loading: false,
            sourcetxt: {
                1: "官方酒会",
                2: "展会",
                3: "其他酒会"
            },
            expands: [],
            sortOptions: [
                {
                    label: "浏览量",
                    value: 1
                },
                {
                    label: "状态",
                    value: 2
                },
                {
                    label: "更新时间 ",
                    value: 3
                }
            ],
            statusOption: [
                {
                    label: "展示中",
                    value: 1
                },
                {
                    label: "未展示",
                    value: 2
                }
            ],
            imgUrl: ""
        };
    },
    created() {
        this.getData();
        this.GetType();
    },
    methods: {
        async GetType() {
            this.$request.winesmel.getArticleType().then(res => {
                if (res.data.error_code == 0) {
                    this.cateOption = res.data.data.list;
                }
            });
        },
        async getData() {
            const params = {
                id: this.query.id,
                keywords: this.query.keywords,
                status: this.query.status,
                stort: this.query.stort,
                cate_id: this.query.cate_id,
                // type: this.types,
                limit: this.pageSize,
                page: this.currentPage
            };
            this.$request.winesmel.articleList(params).then(res => {
                if (res.data.error_code == 0) {
                    res.data.data.list.map(item => {
                        item.is_hot = item.is_hot === 1 ? true : false;
                        item.is_index = item.is_index === 1 ? true : false;
                        item.status = item.status === 1 ? true : false;
                        item.update_name = item.update_name || '系统';
                        item.adname = item.adname === '测试人员' ? '系统' : (item.adname || '系统');
                    });
                    this.DataList = res.data.data.list || [];
                    this.total = res.data.data.total;
                }
            });
        },
        handleClose() {
            this.getData();
            this.dialogStatus = false;
        },
        async changeEnabled(row, status) {
            const params = {
                id: row.id,
                type: 3,
                status
            };

            this.$request.winesmel.updateStatus(params).then(res => {
                if (res.data.error_code == 0) {
                    this.getData();
                    this.$message.success("操作成功！");
                }
            });
        },

        async del(row) {
            this.$confirm("你确定删除嘛?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    console.log(1111111);
                    this.$request.winesmel
                        .delArticle({
                            id: row.id
                        })
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.currentPage = 1;
                                this.getData();
                            }
                        });
                })
                .catch(() => {
                    //几点取消的提示
                });
        },
        async updateSort(row) {
            console.log(row.ordid, 6666);
            if (row.ordid == "" && row.ordid == 0) {
                this.$message.error("排序值必须大于0！");
                return;
            }

            this.$request.winesmel
                .articleSort({
                    id: row.id,
                    ordid: row.ordid
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                    }
                });
        },
        // 编辑
        close() {
            this.getData();
            this.dialogStatus = false;
        },
        handleClick(row) {
            this.dialogStatus = true;

            setTimeout(() => {
                this.$refs.editForm.openForm(row);
            }, 400);
        },

        queryStatus() {
            this.currentPage = 1;
            this.getData();
        },
        // 高级查询
        search() {
            this.currentPage = 1;
            this.getData();
        },
        // 改变每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.getData();
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getData();
        },
        // 清除查询
        clearSelect() {
            this.alllistsearch = {
                goods: "", // 闪购名称
                goodsid: "", // 闪购ID
                kind: "" // 类别
            };
            this.currentPage = 1;
            this.pageSize = 10; // 每页条数
            this.getData();
        },
        formatNumber(num) {
            if (!num) return '0';
            if (num >= 10000000) {
                return (num / 10000000).toFixed(1) + '千万';
            } else if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            } 
            return num.toString();
        },
        showLargeImage(url) {
            this.imgUrl = url;
            this.dialogVisible1 = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.clickable-link {
    &:hover {
        color: #409EFF !important;
        text-decoration: underline !important;
    }
}
</style>
