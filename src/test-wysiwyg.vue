<template>
    <div class="test-wysiwyg">
        <h1>WysiwygEditor 测试页面</h1>
        
        <div class="editor-container">
            <h2>编辑器</h2>
            <WysiwygEditor 
                v-model="content" 
                :height="500"
                @input="handleContentChange"
            />
        </div>
        
        <div class="preview-container">
            <h2>内容预览</h2>
            <pre>{{ content }}</pre>
        </div>
        
        <div class="debug-info">
            <h2>调试信息</h2>
            <p>内容长度: {{ content.length }}</p>
            <p>最后更新时间: {{ lastUpdateTime }}</p>
        </div>
    </div>
</template>

<script>
import WysiwygEditor from '@/components/WysiwygEditor/index.vue'

export default {
    name: 'TestWysiwyg',
    components: {
        WysiwygEditor
    },
    data() {
        return {
            content: '',
            lastUpdateTime: null
        }
    },
    methods: {
        handleContentChange(newContent) {
            console.log('内容变化:', newContent)
            this.lastUpdateTime = new Date().toLocaleTimeString()
        }
    }
}
</script>

<style scoped>
.test-wysiwyg {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.editor-container,
.preview-container,
.debug-info {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 4px;
}

.preview-container pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

h1, h2 {
    color: #333;
}
</style>
